import argparse
import sys

import streamlit.web.cli as stcli

from jetbrains_refresh_token.constants import FRONTEND_APP_PATH
from jetbrains_refresh_token.log_config import get_logger

logger = get_logger("main")


def launch_web_ui(port: int = 8501):
    """
    Launch the Streamlit Web UI.

    Args:
        port (int): Port number for the Web UI. Default is 8501.

    Returns:
        bool: Returns True if launched successfully, otherwise False.
    """
    logger.info("Launching Streamlit Web UI on port %d", port)

    sys.argv = [
        "streamlit",
        "run",
        str(FRONTEND_APP_PATH),
        "--server.port",
        str(port),
        "--server.headless",
        "true",
        "--browser.gatherUsageStats",
        "false",
    ]

    stcli.main()
    return True


def setup_argument_parser():
    parser = argparse.ArgumentParser(
        description='JetBrains JWT Token Refresh Tool',
        epilog='Usage example: python main.py --refresh-access OR python main.py --web',
    )

    parser.add_argument('--refresh-access', type=str, help='Refresh JWT for the specified account')

    parser.add_argument(
        '--refresh-all-access', action='store_true', help='Refresh JWT for all accounts'
    )

    parser.add_argument('--backup', action='store_true', help='Backup configuration file')
    parser.add_argument('--list', action='store_true', help='List all account information')
    parser.add_argument(
        '--export-jetbrainsai',
        type=str,
        nargs='?',
        const='jetbrainsai.json',
        help='Export configuration to jetbrainsai.json format (optionally specify output path)',
    )
    parser.add_argument(
        '--check-quota', action='store_true', help='Check quota remaining for all accounts'
    )
    parser.add_argument(
        '--force', action='store_true', help='Force update tokens (use with refresh options)'
    )

    # Web UI arguments
    parser.add_argument('--web', action='store_true', help='Launch Streamlit web interface')
    parser.add_argument(
        '--web-port', type=int, default=8501, help='Port for web interface (default: 8501)'
    )

    # Scheduler arguments
    parser.add_argument(
        '--scheduler', action='store_true', help='Launch APScheduler background service'
    )
    parser.add_argument(
        '--scheduler-test',
        action='store_true',
        help='Launch scheduler in test mode (fast execution)',
    )

    # Note: Combined service arguments are deprecated. Use Docker Compose instead.

    return parser


def main():
    parser = setup_argument_parser()
    args = parser.parse_args()

    if args.scheduler or args.scheduler_test:
        logger.error("Scheduler mode is deprecated. Please use the module directly:")
        if args.scheduler_test:
            logger.error("  python -m jetbrains_refresh_token.daemon.scheduler_daemon --test")
        else:
            logger.error("  python -m jetbrains_refresh_token.daemon.scheduler_daemon")
        return

    if args.web:
        success = launch_web_ui(args.web_port)
        if success:
            logger.info("Web UI launched successfully on port %d", args.web_port)
        else:
            logger.error("Failed to launch Web UI. Please check the logs.")
        return

    # Show help if no arguments provided
    if len(sys.argv) == 1:
        parser.print_help()
        return


# 主程序入口
if __name__ == "__main__":
    main()
